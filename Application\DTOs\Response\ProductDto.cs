using Domain.Common.Base;

namespace Application.DTOs.Response
{
    public class ProductDto : BaseDTOs
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public decimal Price { get; set; }
        public string? Description { get; set; }
        public int Stock { get; set; }
        public bool IsActive { get; set; }
        public string? CategoryId { get; set; }
        public CategoryDto? Category { get; set; }
    }

    public class ProductListDto : BaseDTOs
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public decimal Price { get; set; }
        public string? Description { get; set; }
        public int Stock { get; set; }
        public bool IsActive { get; set; }
        public string? CategoryId { get; set; }
        public string? CategoryName { get; set; }
    }
}
