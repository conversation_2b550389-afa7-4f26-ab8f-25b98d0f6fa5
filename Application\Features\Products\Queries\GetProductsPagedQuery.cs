using Application.DTOs.Response;
using Domain.Common.Base;
using MediatR;

namespace Application.Features.Products.Queries
{
    public class GetProductsPagedQuery : IRequest<BasePagedResult<ProductListDto>>
    {
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public bool IncludeInactive { get; set; } = false;
        public string? CategoryId { get; set; }
        public string? SearchTerm { get; set; }
    }
}
