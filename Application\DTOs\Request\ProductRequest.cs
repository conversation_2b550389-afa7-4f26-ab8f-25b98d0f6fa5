using System.ComponentModel.DataAnnotations;

namespace Application.DTOs.Request
{
    public class CreateProductRequest
    {
        [Required]
        [StringLength(100, MinimumLength = 1)]
        public string Name { get; set; } = string.Empty;
        
        [Range(0, double.MaxValue, ErrorMessage = "Price must be greater than or equal to 0")]
        public decimal Price { get; set; }
        
        [StringLength(1000)]
        public string? Description { get; set; }
        
        [Range(0, int.MaxValue, ErrorMessage = "Stock must be greater than or equal to 0")]
        public int Stock { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public string? CategoryId { get; set; }
    }

    public class UpdateProductRequest
    {
        [Required]
        public string Id { get; set; } = string.Empty;
        
        [Required]
        [StringLength(100, MinimumLength = 1)]
        public string Name { get; set; } = string.Empty;
        
        [Range(0, double.MaxValue, ErrorMessage = "Price must be greater than or equal to 0")]
        public decimal Price { get; set; }
        
        [StringLength(1000)]
        public string? Description { get; set; }
        
        [Range(0, int.MaxValue, ErrorMessage = "Stock must be greater than or equal to 0")]
        public int Stock { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public string? CategoryId { get; set; }
    }
}
