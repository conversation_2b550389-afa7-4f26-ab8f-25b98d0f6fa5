{"version": 3, "targets": {"net8.0": {"MediatR.Contracts/2.0.1": {"type": "package", "compile": {"lib/netstandard2.0/MediatR.Contracts.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/MediatR.Contracts.dll": {"related": ".xml"}}}}}, "libraries": {"MediatR.Contracts/2.0.1": {"sha512": "FYv95bNT4UwcNA+G/J1oX5OpRiSUxteXaUt2BJbRSdRNiIUNbggJF69wy6mnk2wYToaanpdXZdCwVylt96MpwQ==", "type": "package", "path": "mediatr.contracts/2.0.1", "files": [".nupkg.metadata", ".signature.p7s", "gradient_128x128.png", "lib/netstandard2.0/MediatR.Contracts.dll", "lib/netstandard2.0/MediatR.Contracts.xml", "mediatr.contracts.2.0.1.nupkg.sha512", "mediatr.contracts.nuspec"]}}, "projectFileDependencyGroups": {"net8.0": ["MediatR.Contracts >= 2.0.1"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\FPT\\basePj\\BaseProjectCA\\Domain\\Domain.csproj", "projectName": "Domain", "projectPath": "D:\\FPT\\basePj\\BaseProjectCA\\Domain\\Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\FPT\\basePj\\BaseProjectCA\\Domain\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Users\\<USER>\\.nuget\\packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"MediatR.Contracts": {"target": "Package", "version": "[2.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204/PortableRuntimeIdentifierGraph.json"}}}}