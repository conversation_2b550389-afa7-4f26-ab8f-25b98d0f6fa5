using Application.DTOs.Response;
using Application.Features.Products.Queries;
using Application.Interfaces;
using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.Products.Handlers
{
    public class GetAllProductsQueryHandler : IRequestHandler<GetAllProductsQuery, IEnumerable<ProductListDto>>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public GetAllProductsQueryHandler(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        public async Task<IEnumerable<ProductListDto>> Handle(GetAllProductsQuery request, CancellationToken cancellationToken)
        {
            var query = _unitOfWork.Products.Query()
                .Include(p => p.Category)
                .AsQueryable();

            if (!request.IncludeInactive)
            {
                query = query.Where(p => p.IsActive);
            }

            if (!string.IsNullOrEmpty(request.CategoryId))
            {
                query = query.Where(p => p.CategoryId == request.CategoryId);
            }

            if (!string.IsNullOrEmpty(request.SearchTerm))
            {
                query = query.Where(p => p.Name.Contains(request.SearchTerm) ||
                                        (p.Description != null && p.Description.Contains(request.SearchTerm)));
            }

            var products = await query.ToListAsync(cancellationToken);
            return _mapper.Map<IEnumerable<ProductListDto>>(products);
        }
    }
}
