using Application.DTOs.Response;
using Application.Features.Products.Commands;
using Application.Interfaces;
using AutoMapper;
using MediatR;

namespace Application.Features.Products.Handlers
{
    public class UpdateProductCommandHandler : IRequestHandler<UpdateProductCommand, ProductResponse>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public UpdateProductCommandHandler(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        public async Task<ProductResponse> Handle(UpdateProductCommand request, CancellationToken cancellationToken)
        {
            var product = await _unitOfWork.Products.GetByIdAsync(request.Id);
            
            if (product == null)
                throw new KeyNotFoundException($"Product with ID {request.Id} not found.");

            _mapper.Map(request, product);
            
            _unitOfWork.Products.Update(product);
            await _unitOfWork.SaveAsync(cancellationToken);

            return _mapper.Map<ProductResponse>(product);
        }
    }
}
