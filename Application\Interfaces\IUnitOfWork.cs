﻿using Domain.Entities;
using Microsoft.EntityFrameworkCore.Storage;

namespace Application.Interfaces
{
    public interface IUnitOfWork : IDisposable
    {
        IGenericRepository<Product> Products { get; }
        IGenericRepository<Category> Categories { get; }

        IGenericRepository<T> Repository<T>() where T : class;
        Task<int> SaveAsync(CancellationToken cancellationToken = default);
        Task<IDbContextTransaction> BeginTransactionAsync(CancellationToken cancellationToken = default);
        Task CommitTransactionAsync(CancellationToken cancellationToken = default);
        Task RollbackTransactionAsync(CancellationToken cancellationToken = default);
        Task<int> ExecuteSqlAsync(string sql, params object[] parameters);
        Task<int> ExecuteSqlAsync(string sql, CancellationToken cancellationToken, params object[] parameters);
    }
}
