using Application.DTOs.Response;
using MediatR;

namespace Application.Features.Products.Commands
{
    public class CreateProductCommand : IRequest<ProductResponse>
    {
        public string Name { get; set; } = string.Empty;
        public decimal Price { get; set; }
        public string? Description { get; set; }
        public int Stock { get; set; }
        public bool IsActive { get; set; } = true;
        public string? CategoryId { get; set; }
    }
}
