D:\FPT\basePj\BaseProjectCA\WebAPI\obj\Debug\net8.0\WebAPI.csproj.AssemblyReference.cache
D:\FPT\basePj\BaseProjectCA\WebAPI\obj\Debug\net8.0\WebAPI.GeneratedMSBuildEditorConfig.editorconfig
D:\FPT\basePj\BaseProjectCA\WebAPI\obj\Debug\net8.0\WebAPI.AssemblyInfoInputs.cache
D:\FPT\basePj\BaseProjectCA\WebAPI\obj\Debug\net8.0\WebAPI.AssemblyInfo.cs
D:\FPT\basePj\BaseProjectCA\WebAPI\obj\Debug\net8.0\WebAPI.csproj.CoreCompileInputs.cache
D:\FPT\basePj\BaseProjectCA\WebAPI\obj\Debug\net8.0\WebAPI.MvcApplicationPartsAssemblyInfo.cs
D:\FPT\basePj\BaseProjectCA\WebAPI\obj\Debug\net8.0\WebAPI.MvcApplicationPartsAssemblyInfo.cache
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\appsettings.Development.json
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\appsettings.json
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\WebAPI.exe
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\WebAPI.deps.json
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\WebAPI.runtimeconfig.json
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\WebAPI.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\WebAPI.pdb
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\AutoMapper.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\AutoMapper.Extensions.Microsoft.DependencyInjection.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Azure.Core.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Azure.Identity.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\FluentValidation.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\FluentValidation.AspNetCore.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\FluentValidation.DependencyInjectionExtensions.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\MediatR.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\MediatR.Contracts.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Microsoft.Bcl.AsyncInterfaces.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Microsoft.Data.SqlClient.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Abstractions.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Relational.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.SqlServer.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Microsoft.Extensions.Caching.Abstractions.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Microsoft.Extensions.Caching.Memory.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Microsoft.Extensions.Configuration.Abstractions.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Microsoft.Extensions.DependencyInjection.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Microsoft.Extensions.Logging.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Microsoft.Extensions.Logging.Abstractions.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Microsoft.Extensions.Options.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Microsoft.Extensions.Primitives.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Microsoft.Identity.Client.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Microsoft.Identity.Client.Extensions.Msal.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Microsoft.IdentityModel.Abstractions.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Microsoft.IdentityModel.JsonWebTokens.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Microsoft.IdentityModel.Logging.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Microsoft.IdentityModel.Tokens.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Microsoft.OpenApi.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Microsoft.SqlServer.Server.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Microsoft.Win32.SystemEvents.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Swashbuckle.AspNetCore.Swagger.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerGen.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerUI.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\System.ClientModel.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\System.Configuration.ConfigurationManager.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\System.Diagnostics.DiagnosticSource.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\System.Drawing.Common.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\System.Formats.Asn1.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\System.IdentityModel.Tokens.Jwt.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\System.IO.Pipelines.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\System.Memory.Data.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\System.Runtime.Caching.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\System.Security.Cryptography.ProtectedData.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\System.Security.Permissions.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\System.Text.Encodings.Web.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\System.Text.Json.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\System.Windows.Extensions.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\runtimes\unix\lib\net6.0\Microsoft.Data.SqlClient.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\runtimes\win\lib\net6.0\Microsoft.Data.SqlClient.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\runtimes\win-arm\native\Microsoft.Data.SqlClient.SNI.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\runtimes\win-arm64\native\Microsoft.Data.SqlClient.SNI.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\runtimes\win-x64\native\Microsoft.Data.SqlClient.SNI.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\runtimes\win-x86\native\Microsoft.Data.SqlClient.SNI.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\runtimes\win\lib\net6.0\Microsoft.Win32.SystemEvents.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\runtimes\unix\lib\net6.0\System.Drawing.Common.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\runtimes\win\lib\net6.0\System.Drawing.Common.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\runtimes\win\lib\net6.0\System.Runtime.Caching.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\runtimes\win\lib\net6.0\System.Security.Cryptography.ProtectedData.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\runtimes\browser\lib\net8.0\System.Text.Encodings.Web.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\runtimes\win\lib\net6.0\System.Windows.Extensions.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Application.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Domain.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Persistence.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Application.pdb
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Persistence.pdb
D:\FPT\basePj\BaseProjectCA\WebAPI\bin\Debug\net8.0\Domain.pdb
D:\FPT\basePj\BaseProjectCA\WebAPI\obj\Debug\net8.0\staticwebassets.build.json
D:\FPT\basePj\BaseProjectCA\WebAPI\obj\Debug\net8.0\staticwebassets.development.json
D:\FPT\basePj\BaseProjectCA\WebAPI\obj\Debug\net8.0\staticwebassets\msbuild.WebAPI.Microsoft.AspNetCore.StaticWebAssets.props
D:\FPT\basePj\BaseProjectCA\WebAPI\obj\Debug\net8.0\staticwebassets\msbuild.build.WebAPI.props
D:\FPT\basePj\BaseProjectCA\WebAPI\obj\Debug\net8.0\staticwebassets\msbuild.buildMultiTargeting.WebAPI.props
D:\FPT\basePj\BaseProjectCA\WebAPI\obj\Debug\net8.0\staticwebassets\msbuild.buildTransitive.WebAPI.props
D:\FPT\basePj\BaseProjectCA\WebAPI\obj\Debug\net8.0\staticwebassets.pack.json
D:\FPT\basePj\BaseProjectCA\WebAPI\obj\Debug\net8.0\scopedcss\bundle\WebAPI.styles.css
D:\FPT\basePj\BaseProjectCA\WebAPI\obj\Debug\net8.0\WebAPI.csproj.Up2Date
D:\FPT\basePj\BaseProjectCA\WebAPI\obj\Debug\net8.0\WebAPI.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\obj\Debug\net8.0\refint\WebAPI.dll
D:\FPT\basePj\BaseProjectCA\WebAPI\obj\Debug\net8.0\WebAPI.pdb
D:\FPT\basePj\BaseProjectCA\WebAPI\obj\Debug\net8.0\WebAPI.genruntimeconfig.cache
D:\FPT\basePj\BaseProjectCA\WebAPI\obj\Debug\net8.0\ref\WebAPI.dll
