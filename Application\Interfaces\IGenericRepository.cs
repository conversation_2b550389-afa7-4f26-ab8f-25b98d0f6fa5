﻿namespace Application.Interfaces
{
    public interface IGenericRepository<T> where T : class
    {
        IQueryable<T> Query(bool track = false);
        Task<IEnumerable<T>> GetAllAsync();
        Task<T?> GetByIdAsync(object id);
        Task AddAsync(T entity);
        Task AddRangeAsync(IList<T> entities);
        void Update(T entity);
        void UpdateRange(IList<T> entities);
        void Delete(T entity);
        void DeleteRange(IList<T> entities);
    }
}
