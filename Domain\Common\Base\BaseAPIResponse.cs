﻿
namespace Domain.Common.Base
{
    public class BaseAPIResponse<T>
    {
        public int StatusCode { get; set; }
        public string Message { get; set; } = string.Empty;
        public T Data { get; set; } = default!;
        public DateTime DateTime { get; set; }
        public string TimeCount { get; set; } = string.Empty;

        public BaseAPIResponse()
        {
            DateTime = DateTime.UtcNow;
        }
    }
}
