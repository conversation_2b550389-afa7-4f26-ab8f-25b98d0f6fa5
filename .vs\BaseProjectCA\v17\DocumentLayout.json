{"Version": 1, "WorkspaceRootPath": "D:\\FPT\\basePj\\BaseProjectCA\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{F50E3DD2-5FCC-4C35-B0F6-61881E0FAE03}|WebAPI\\WebAPI.csproj|d:\\fpt\\basepj\\baseprojectca\\webapi\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F50E3DD2-5FCC-4C35-B0F6-61881E0FAE03}|WebAPI\\WebAPI.csproj|solutionrelative:webapi\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{28E52897-AF61-4A05-A207-95F61B46CA7E}|Persistence\\Persistence.csproj|d:\\fpt\\basepj\\baseprojectca\\persistence\\services\\currentuserservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{28E52897-AF61-4A05-A207-95F61B46CA7E}|Persistence\\Persistence.csproj|solutionrelative:persistence\\services\\currentuserservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{28E52897-AF61-4A05-A207-95F61B46CA7E}|Persistence\\Persistence.csproj|d:\\fpt\\basepj\\baseprojectca\\persistence\\seeders\\databaseseeder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{28E52897-AF61-4A05-A207-95F61B46CA7E}|Persistence\\Persistence.csproj|solutionrelative:persistence\\seeders\\databaseseeder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{28E52897-AF61-4A05-A207-95F61B46CA7E}|Persistence\\Persistence.csproj|d:\\fpt\\basepj\\baseprojectca\\persistence\\extensions\\servicecollectionextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{28E52897-AF61-4A05-A207-95F61B46CA7E}|Persistence\\Persistence.csproj|solutionrelative:persistence\\extensions\\servicecollectionextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{28E52897-AF61-4A05-A207-95F61B46CA7E}|Persistence\\Persistence.csproj|d:\\fpt\\basepj\\baseprojectca\\persistence\\context\\applicationdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{28E52897-AF61-4A05-A207-95F61B46CA7E}|Persistence\\Persistence.csproj|solutionrelative:persistence\\context\\applicationdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{28E52897-AF61-4A05-A207-95F61B46CA7E}|Persistence\\Persistence.csproj|d:\\fpt\\basepj\\baseprojectca\\persistence\\configurations\\productconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{28E52897-AF61-4A05-A207-95F61B46CA7E}|Persistence\\Persistence.csproj|solutionrelative:persistence\\configurations\\productconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{28E52897-AF61-4A05-A207-95F61B46CA7E}|Persistence\\Persistence.csproj|d:\\fpt\\basepj\\baseprojectca\\persistence\\configurations\\categoryconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{28E52897-AF61-4A05-A207-95F61B46CA7E}|Persistence\\Persistence.csproj|solutionrelative:persistence\\configurations\\categoryconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 8, "Children": [{"$type": "Bookmark", "Name": "ST:11:0:{e8034f19-ab72-4f06-83fd-f6832b41aa63}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:2:0:{e8034f19-ab72-4f06-83fd-f6832b41aa63}"}, {"$type": "Bookmark", "Name": "ST:3:0:{e8034f19-ab72-4f06-83fd-f6832b41aa63}"}, {"$type": "Bookmark", "Name": "ST:4:0:{e8034f19-ab72-4f06-83fd-f6832b41aa63}"}, {"$type": "Bookmark", "Name": "ST:128:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:12:0:{e8034f19-ab72-4f06-83fd-f6832b41aa63}"}, {"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "AuthController.cs", "DocumentMoniker": "D:\\FPT\\basePj\\BaseProjectCA\\WebAPI\\Controllers\\AuthController.cs", "RelativeDocumentMoniker": "WebAPI\\Controllers\\AuthController.cs", "ToolTip": "D:\\FPT\\basePj\\BaseProjectCA\\WebAPI\\Controllers\\AuthController.cs", "RelativeToolTip": "WebAPI\\Controllers\\AuthController.cs", "ViewState": "AQIAAIQAAAAAAAAAAAAowAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T03:45:39.482Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "CurrentUserService.cs", "DocumentMoniker": "D:\\FPT\\basePj\\BaseProjectCA\\Persistence\\Services\\CurrentUserService.cs", "RelativeDocumentMoniker": "Persistence\\Services\\CurrentUserService.cs", "ToolTip": "D:\\FPT\\basePj\\BaseProjectCA\\Persistence\\Services\\CurrentUserService.cs", "RelativeToolTip": "Persistence\\Services\\CurrentUserService.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T03:45:36.151Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "DatabaseSeeder.cs", "DocumentMoniker": "D:\\FPT\\basePj\\BaseProjectCA\\Persistence\\Seeders\\DatabaseSeeder.cs", "RelativeDocumentMoniker": "Persistence\\Seeders\\DatabaseSeeder.cs", "ToolTip": "D:\\FPT\\basePj\\BaseProjectCA\\Persistence\\Seeders\\DatabaseSeeder.cs", "RelativeToolTip": "Persistence\\Seeders\\DatabaseSeeder.cs", "ViewState": "AQIAAB4AAAAAAAAAAAAYwAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T03:45:08.775Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "ServiceCollectionExtensions.cs", "DocumentMoniker": "D:\\FPT\\basePj\\BaseProjectCA\\Persistence\\Extensions\\ServiceCollectionExtensions.cs", "RelativeDocumentMoniker": "Persistence\\Extensions\\ServiceCollectionExtensions.cs", "ToolTip": "D:\\FPT\\basePj\\BaseProjectCA\\Persistence\\Extensions\\ServiceCollectionExtensions.cs", "RelativeToolTip": "Persistence\\Extensions\\ServiceCollectionExtensions.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T03:45:06.699Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "ApplicationDbContext.cs", "DocumentMoniker": "D:\\FPT\\basePj\\BaseProjectCA\\Persistence\\Context\\ApplicationDbContext.cs", "RelativeDocumentMoniker": "Persistence\\Context\\ApplicationDbContext.cs", "ToolTip": "D:\\FPT\\basePj\\BaseProjectCA\\Persistence\\Context\\ApplicationDbContext.cs", "RelativeToolTip": "Persistence\\Context\\ApplicationDbContext.cs", "ViewState": "AQIAAGgAAAAAAAAAAAAswBsAAAAJAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T03:44:29.375Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "ProductConfiguration.cs", "DocumentMoniker": "D:\\FPT\\basePj\\BaseProjectCA\\Persistence\\Configurations\\ProductConfiguration.cs", "RelativeDocumentMoniker": "Persistence\\Configurations\\ProductConfiguration.cs", "ToolTip": "D:\\FPT\\basePj\\BaseProjectCA\\Persistence\\Configurations\\ProductConfiguration.cs", "RelativeToolTip": "Persistence\\Configurations\\ProductConfiguration.cs", "ViewState": "AQIAAA0AAAAAAAAAAAAQwAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T03:44:27.142Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "CategoryConfiguration.cs", "DocumentMoniker": "D:\\FPT\\basePj\\BaseProjectCA\\Persistence\\Configurations\\CategoryConfiguration.cs", "RelativeDocumentMoniker": "Persistence\\Configurations\\CategoryConfiguration.cs", "ToolTip": "D:\\FPT\\basePj\\BaseProjectCA\\Persistence\\Configurations\\CategoryConfiguration.cs", "RelativeToolTip": "Persistence\\Configurations\\CategoryConfiguration.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T03:44:23.438Z", "EditorCaption": ""}]}]}]}