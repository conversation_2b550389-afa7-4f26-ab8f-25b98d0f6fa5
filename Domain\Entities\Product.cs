﻿using Domain.Common.Base;
using System.ComponentModel.DataAnnotations;

namespace Domain.Entities
{
    public class Product : BaseEntity
    {
        [Required]
        public string Name { get; set; } = string.Empty;

        [Range(0, double.MaxValue, ErrorMessage = "Price must be greater than or equal to 0")]
        public decimal Price { get; set; }

        public string? Description { get; set; }

        public int Stock { get; set; }

        public bool IsActive { get; set; } = true;

        public string? CategoryId { get; set; }
        public virtual Category? Category { get; set; }
    }
}
