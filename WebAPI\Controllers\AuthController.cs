using Application.Common.Interfaces;
using Application.DTOs.Request;
using Application.DTOs.Response;
using Application.Features.Auth.Commands;
using AutoMapper;
using Domain.Common.Base;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace WebAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly IMapper _mapper;
        private readonly IJwtService _jwtService;

        public AuthController(IMediator mediator, IMapper mapper, IJwtService jwtService)
        {
            _mediator = mediator;
            _mapper = mapper;
            _jwtService = jwtService;
        }

        /// <summary>
        /// User login
        /// </summary>
        [HttpPost("login")]
        public async Task<ActionResult<BaseAPIResponse<AuthResponse>>> Login([FromBody] LoginRequest request)
        {
            try
            {
                var command = _mapper.Map<LoginCommand>(request);
                var result = await _mediator.Send(command);

                return Ok(new BaseAPIResponse<AuthResponse>
                {
                    StatusCode = 200,
                    Message = "Login successful",
                    Data = result
                });
            }
            catch (UnauthorizedAccessException ex)
            {
                return Unauthorized(new BaseAPIResponse<AuthResponse>
                {
                    StatusCode = 401,
                    Message = ex.Message
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new BaseAPIResponse<AuthResponse>
                {
                    StatusCode = 400,
                    Message = ex.Message
                });
            }
        }

        /// <summary>
        /// User registration
        /// </summary>
        [HttpPost("register")]
        public async Task<ActionResult<BaseAPIResponse<AuthResponse>>> Register([FromBody] RegisterRequest request)
        {
            try
            {
                var command = _mapper.Map<RegisterCommand>(request);
                var result = await _mediator.Send(command);

                return Ok(new BaseAPIResponse<AuthResponse>
                {
                    StatusCode = 201,
                    Message = "Registration successful",
                    Data = result
                });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new BaseAPIResponse<AuthResponse>
                {
                    StatusCode = 400,
                    Message = ex.Message
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new BaseAPIResponse<AuthResponse>
                {
                    StatusCode = 400,
                    Message = ex.Message
                });
            }
        }

        /// <summary>
        /// Refresh access token
        /// </summary>
        [HttpPost("refresh-token")]
        public async Task<ActionResult<BaseAPIResponse<AuthResponse>>> RefreshToken([FromBody] RefreshTokenRequest request)
        {
            try
            {
                var result = await _jwtService.RefreshTokenAsync(request.AccessToken, request.RefreshToken);

                return Ok(new BaseAPIResponse<AuthResponse>
                {
                    StatusCode = 200,
                    Message = "Token refreshed successfully",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return Unauthorized(new BaseAPIResponse<AuthResponse>
                {
                    StatusCode = 401,
                    Message = ex.Message
                });
            }
        }

        /// <summary>
        /// Get current user profile
        /// </summary>
        [HttpGet("profile")]
        [Authorize]
        public async Task<ActionResult<BaseAPIResponse<UserDto>>> GetProfile()
        {
            try
            {
                var userId = User.FindFirst("sub")?.Value ?? User.FindFirst("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier")?.Value;
                if (string.IsNullOrEmpty(userId))
                    return Unauthorized();

                // Here you would get user details from UserManager
                // For now, return basic info from claims
                var userDto = new UserDto
                {
                    Id = userId,
                    Email = User.FindFirst("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress")?.Value ?? "",
                    FirstName = User.FindFirst("FirstName")?.Value ?? "",
                    LastName = User.FindFirst("LastName")?.Value ?? "",
                    FullName = User.FindFirst("FullName")?.Value ?? "",
                    Roles = User.FindAll("http://schemas.microsoft.com/ws/2008/06/identity/claims/role").Select(c => c.Value).ToList()
                };

                return Ok(new BaseAPIResponse<UserDto>
                {
                    StatusCode = 200,
                    Message = "Profile retrieved successfully",
                    Data = userDto
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new BaseAPIResponse<UserDto>
                {
                    StatusCode = 400,
                    Message = ex.Message
                });
            }
        }
    }
}
