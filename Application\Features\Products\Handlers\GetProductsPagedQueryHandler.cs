using Application.DTOs.Response;
using Application.Features.Products.Queries;
using Application.Interfaces;
using AutoMapper;
using Domain.Common.Base;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.Products.Handlers
{
    public class GetProductsPagedQueryHandler : IRequestHandler<GetProductsPagedQuery, BasePagedResult<ProductListDto>>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public GetProductsPagedQueryHandler(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        public async Task<BasePagedResult<ProductListDto>> Handle(GetProductsPagedQuery request, CancellationToken cancellationToken)
        {
            var query = _unitOfWork.Products.Query()
                .Include(p => p.Category)
                .AsQueryable();

            if (!request.IncludeInactive)
            {
                query = query.Where(p => p.IsActive);
            }

            if (!string.IsNullOrEmpty(request.CategoryId))
            {
                query = query.Where(p => p.CategoryId == request.CategoryId);
            }

            if (!string.IsNullOrEmpty(request.SearchTerm))
            {
                query = query.Where(p => p.Name.Contains(request.SearchTerm) ||
                                        (p.Description != null && p.Description.Contains(request.SearchTerm)));
            }

            var totalCount = await query.CountAsync(cancellationToken);

            var products = await query
                .Skip((request.PageNumber - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToListAsync(cancellationToken);

            var productDtos = _mapper.Map<IEnumerable<ProductListDto>>(products);

            return new BasePagedResult<ProductListDto>
            {
                Data = productDtos,
                PageNumber = request.PageNumber,
                PageSize = request.PageSize,
                TotalCount = totalCount
            };
        }
    }
}
