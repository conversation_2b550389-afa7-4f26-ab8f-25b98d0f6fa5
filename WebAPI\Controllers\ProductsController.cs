using Application.DTOs.Request;
using Application.DTOs.Response;
using Application.Features.Products.Commands;
using Application.Features.Products.Queries;
using AutoMapper;
using Domain.Common.Base;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace WebAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ProductsController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly IMapper _mapper;

        public ProductsController(IMediator mediator, IMapper mapper)
        {
            _mediator = mediator;
            _mapper = mapper;
        }

        /// <summary>
        /// Get all products
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<ProductListDto>>> GetProducts(
            [FromQuery] bool includeInactive = false,
            [FromQuery] string? categoryId = null,
            [FromQuery] string? searchTerm = null)
        {
            var query = new GetAllProductsQuery
            {
                IncludeInactive = includeInactive,
                CategoryId = categoryId,
                SearchTerm = searchTerm
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }

        /// <summary>
        /// Get products with pagination
        /// </summary>
        [HttpGet("paged")]
        public async Task<ActionResult<BasePagedResult<ProductListDto>>> GetProductsPaged(
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 10,
            [FromQuery] bool includeInactive = false,
            [FromQuery] string? categoryId = null,
            [FromQuery] string? searchTerm = null)
        {
            var query = new GetProductsPagedQuery
            {
                PageNumber = pageNumber,
                PageSize = pageSize,
                IncludeInactive = includeInactive,
                CategoryId = categoryId,
                SearchTerm = searchTerm
            };

            var result = await _mediator.Send(query);
            return Ok(result);
        }

        /// <summary>
        /// Get product by ID
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<ProductDto>> GetProduct(string id)
        {
            var query = new GetProductByIdQuery(id);
            var result = await _mediator.Send(query);

            if (result == null)
                return NotFound($"Product with ID {id} not found.");

            return Ok(result);
        }

        /// <summary>
        /// Create a new product
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<ProductDto>> CreateProduct([FromBody] CreateProductRequest request)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var command = _mapper.Map<CreateProductCommand>(request);
            var result = await _mediator.Send(command);

            return CreatedAtAction(nameof(GetProduct), new { id = result.Id }, result);
        }

        /// <summary>
        /// Update an existing product
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<ProductDto>> UpdateProduct(string id, [FromBody] UpdateProductRequest request)
        {
            if (id != request.Id)
                return BadRequest("ID in URL does not match ID in request body.");

            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                var command = _mapper.Map<UpdateProductCommand>(request);
                var result = await _mediator.Send(command);
                return Ok(result);
            }
            catch (KeyNotFoundException)
            {
                return NotFound($"Product with ID {id} not found.");
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Delete a product
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteProduct(string id)
        {
            var command = new DeleteProductCommand(id);
            var result = await _mediator.Send(command);

            if (!result)
                return NotFound($"Product with ID {id} not found.");

            return NoContent();
        }
    }
}
